import usePermissionStore from '@/pages/splash/permissionStore';
import { getSession, menuListQueryPost } from '@/services/common';
import usePrintStore from '@/stores/print';
import { App as CapacitorApp } from '@capacitor/app';
import { ConfigProvider } from '@nutui/nutui-react-taro';
import en from '@nutui/nutui-react-taro/dist/locales/en-US';
import Taro, { useDidHide, useDidShow } from '@tarojs/taro';
import { PropsWithChildren, useEffect } from 'react';
import { IntlProvider } from 'react-intl';
import 'windi.css';
import './app.scss';
import locale from './locales';
import { queryUserInfoPost } from './pages/mine/services';
import useUserStore from './stores/user';
let lastBack = 0;

function App({ children }: PropsWithChildren<any>) {
  const printStore = usePrintStore();
  const userStore = useUserStore();
  const permission = usePermissionStore();

  useEffect(() => {
    CapacitorApp.addListener('backButton', () => {
      const pages = Taro.getCurrentPages?.();
      if (pages && pages.length > 1) {
        Taro.navigateBack();
      } else {
        const now = Date.now();
        if (now - lastBack < 2000) {
          CapacitorApp.exitApp();
        } else {
          lastBack = now;
          Taro.showToast({
            title: '再按一次退出 App',
            icon: 'none',
            duration: 1500,
          });
        }
      }
    });
    return () => {
      CapacitorApp.removeAllListeners();
    };
  }, []);

  useDidShow(() => {
    getSession().then(async (result) => {
      const currentPage = Taro.getCurrentPages().slice(-1)[0];
      const currentRoute = currentPage?.route || '';
      const isLoginPage = currentRoute.includes('pages/login/index');
      const isSplashPage = currentRoute.includes('pages/splash/index');
      if (result?.data?.accountId) {
        //加载权限
        const data = await menuListQueryPost({ type: '2' });
        if (data) {
          permission.setButtonItem(data);
        }
        if (isLoginPage || isSplashPage) {
          Taro.switchTab({ url: '/pages/home/<USER>' });
        }
        queryUserInfoPost({}).then((res) => {
          userStore.setUserInfo(res);
        });
      } else {
        Taro.redirectTo({ url: '/pages/login/index' });
      }
    });
    // wx.onBLEConnectionStateChange(printStore.listener);
  });

  useDidHide(() => {
    // wx.offBLEConnectionStateChange(printStore.listener);
  });

  // children 是将要会渲染的页面
  return (
    <ConfigProvider locale={en}>
      {/** @ts-ignore **/}
      <IntlProvider messages={locale['en-US']} locale="en" defaultLocale="en">
        {children}
      </IntlProvider>
    </ConfigProvider>
  );
}

export default App;
