import { CustomerListItem } from '@/components/ChooseCstModal/types/CustomerListItem';
import defaultIcon from '@/components/CstCard/imgs/default.svg';
import { Checklist } from '@nutui/icons-react-taro';
import { Tag } from '@nutui/nutui-react-taro';
import { Image } from '@tarojs/components';

export interface CstItemProps {
  record: CustomerListItem;
  onSelect: (value: CustomerListItem) => void;
  cstId?: string;
}

const CstItem = (props: CstItemProps) => {
  const { record, onSelect, cstId } = props;

  const defaultContact = record?.contacts?.[0];

  return (
    <div
      className="flex flex-shrink-0 justify-center mx-[28px] py-[28px]"
      onClick={() => onSelect(record)}
    >
      <Image
        src={record.url ?? defaultIcon}
        style={{ width: '70px', height: '70px' }}
        mode={'aspectFit'}
      />
      <div className="flex-1">
        <div className="text-[32px] leading-[1.6] flex items-center flex-wrap">
          <span className="mr-2">{record.cstName}</span>
          <div className="flex gap-[12px] flex-wrap">
            {record.settle?.settleType && <Tag type="primary">{record.settle?.settleType}</Tag>}
            {record.tags?.map((item) => (
              <Tag type="info">{item.tagName}</Tag>
            ))}
          </div>
        </div>
        <div className="text-thirdary text-[28px] leading-[1.6]">
          <div>
            {defaultContact?.firstName
              ? `${defaultContact.firstName} ${defaultContact?.lastName}`
              : '暂无联系人'}{' '}
            | {defaultContact?.phone ?? '暂无联系方式'}
          </div>
          <div className="flex flex-col gap-y-1.5 border-top-[2px] border-solid border-gray-100 pt-2 mt-1">
            <div className="flex flex-wrap gap-x-3 gap-y-1.5">
              <div className="text-[26px] text-thirdary">归属门店: {record.storeName};</div>
              <div className="text-[26px] text-thirdary">Suburb: TODO</div>
            </div>
            {record?.settle?.credit && (
              <div className="text-[26px] text-thirdary">
                信用额度:
                {record.settle?.totalAmount ?? '-'}/可用
                {record.settle?.availableAmount ?? '-'}
              </div>
            )}
            <div className="flex flex-wrap gap-x-3 gap-y-1.5">
              <div className="text-[26px] text-thirdary">
                客户应收:
                {record.settle?.receivableAmountCurrency ?? '-'};
              </div>
              <div className="text-[26px] text-thirdary">
                客户预收:
                {record.settle?.advanceAmountCurrency ?? '-'}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center">
        {cstId === record.cstId && <Checklist className="mr-1" color="red" />}
      </div>
    </div>
  );
};

export default CstItem;
