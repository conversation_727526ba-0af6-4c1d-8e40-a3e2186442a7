export interface CustomerListItem {
  /**
   * abn
   */
  abn?: string;
  /**
   * 联系人返回一个
   */
  contacts?: Contact[];
  /**
   * 客户创建时间
   */
  createTime?: string;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 客户名称
   */
  cstName?: string;
  /**
   * 客户编码
   */
  cstSn?: string;
  /**
   * 0=启用1=禁用
   */
  cstStatus?: number;
  /**
   * 客户状态名称
   */
  cstStatusName?: string;
  /**
   * 财务邮箱
   */
  financeEmail?: string;
  /**
   * 客户简称
   */
  nickName?: string;
  /**
   * 客户备注
   */
  remark?: string;
  /**
   * 业务员ID
   */
  salesmanId?: string;
  /**
   * 业务员名
   */
  salesmanName?: string;
  /**
   * 发送邮件选项
   */
  sendEmailOptions?: number;
  /**
   * 客户挂账&应收信息
   */
  settle?: Settle;
  /**
   * 归属门店ID
   */
  storeId?: string;
  /**
   * 归属门店名称
   */
  storeName?: string;
  /**
   * 客户标签
   */
  tags?: Tag[];
  /**
   * 通用邮箱
   */
  universalEmail?: string;
}

export interface Contact {
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 邮箱地址
   */
  email?: string;
  /**
   * 联系人姓名
   */
  firstName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 联系人姓名
   */
  lastName?: string;
  /**
   * 客户联系方式
   */
  phone?: string;
  /**
   * QQ号码
   */
  qq?: string;
  /**
   * 联系人备注
   */
  remark?: string;
  /**
   * 微信号
   */
  wechat?: string;
}

/**
 * 客户挂账&应收信息
 */
export interface Settle {
  /**
   * 可用额度，单位：元
   */
  availableAmount?: number;
  receivableAmountCurrency?: number;
  advanceAmountCurrency?: number;
  /**
   * 是否挂账
   */
  credit?: boolean;
  /**
   * 信用账期
   */
  creditTerms?: number;
  /**
   * 客户不收税，0为收税1为不收税
   */
  gstExcluded?: number;
  /**
   * 是否多币种1=多币种0=单币种
   */
  isMultiCurrency?: number;
  /**
   * 应收额度，单位：元
   */
  receivableAmount?: number;
  /**
   * 剩余账期
   */
  remainTerms?: number;
  /**
   * 结算类型CODWeeklyMonthly
   */
  settleType?: string;
  /**
   * 总额度，单位：元
   */
  totalAmount?: number;
  /**
   * 已用额度，单位：元
   */
  usedAmount?: number;
}

export interface Tag {
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 标签ID
   */
  tagId?: string;
  /**
   * 标签名称
   */
  tagName?: string;
}
