import { queryMemberAccountPage } from '@/components/AccountSelectModal/services';
import Card from '@/components/Card';
import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import CustomPicker from '@/components/CustomPicker';
import RadioButtonGroup from '@/components/RadioButtonGroup';
import { PayChannel } from '@/packages/sales/order/edit/components/Settle/types/PayChannel';
import {
  PayKind,
  payKindOptions,
} from '@/packages/sales/order/edit/components/Settle/types/PayKind';
import { updateOrderPayKind } from '@/packages/sales/order/edit/services';
import { ConfirmPayRequest } from '@/packages/sales/order/edit/types/confirm.pay.request';
import { UpdateOrderPayKindRequest } from '@/packages/sales/order/edit/types/update.order.pay.kind.request';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { ArrowDown, IconFont, Trash } from '@nutui/icons-react-taro';
import { Divider, Input } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import _ from 'lodash';
import { useEffect, useRef, useState } from 'react';
import addIcon from './imgs/add.svg';

export interface CashListItem {
  payeeAcount: string;
  payAmount: string | undefined;
  key: string;
}

export interface SettleProps {
  orderDetail?: OrderListItemEntity;
  cstDetail?: CustomerEntity;
  onRefresh?: () => void;
  className?: string;
  onChange?: (data: UpdateOrderPayKindRequest) => void;
}

const Settle = (props: SettleProps) => {
  const needUpdate = useRef(true);
  const { orderDetail, className, cstDetail, onRefresh, onChange } = props;
  const [accountList, setAccountList] = useState<any[]>([]);
  const [cashList, setCashList] = useState<CashListItem[]>([]);
  const [payKind, setPayKind] = useState<PayKind>();
  const [accountPickerProps, setAccountPickerProps] = useState<{
    visible: boolean;
    index?: number;
  }>({
    visible: false,
    index: undefined,
  });

  const storeId = orderDetail?.orders?.storeId;

  useEffect(() => {
    if (orderDetail) {
      setPayKind(orderDetail?.orderPayDetailList?.[0]?.payKind);
      if (orderDetail?.orderPayDetailList?.[0]?.payKind === PayKind.Cash) {
        const payDetailList: CashListItem[] = [];
        orderDetail?.orderPayDetailList?.forEach((item) => {
          if (
            typeof item.payAmountYuan !== 'undefined' &&
            typeof item.payeeAccount !== 'undefined'
          ) {
            payDetailList.push({
              payeeAcount: item.payeeAccount,
              payAmount: item.payAmountYuan?.toString(),
              key: item.id!,
            });
          }
        });
        if (payDetailList.length) {
          setCashList(payDetailList);
        }
      }
    } else {
      setPayKind(cstDetail?.settle?.credit ? PayKind.Credit : PayKind.Cash);
    }
  }, [orderDetail]);

  useEffect(() => {
    if (storeId) {
      queryMemberAccountPage({
        belongToStore: [storeId],
      }).then((result) => {
        if (result?.data?.length) {
          setAccountList(
            result.data.map((item) => ({
              title: item.memberAccountName,
              value: item.id,
            })),
          );
        }
      });
    }
  }, [storeId]);

  useEffect(() => {
    if (!orderDetail) {
      return;
    }
    const payKindParams = {
      orderId: orderDetail?.orderId,
      payKind: payKind,
      payDetailList: [],
    } as ConfirmPayRequest;
    switch (payKind) {
      case PayKind.Credit:
        payKindParams.payDetailList?.push({
          payChannel: PayChannel.PAYMENT_DAYS,
          payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
        });
        if (onChange) {
          onChange(payKindParams);
        } else {
          if (payKind !== orderDetail.orderPayDetailList?.[0].payKind) {
            updateOrderPayKind(payKindParams).then((result) => {
              if (result) {
                onRefresh?.();
              }
            });
          }
        }
        break;
      case PayKind.Cash:
        if (orderDetail.orderPayDetailList?.[0]?.payKind === PayKind.Credit) {
          setCashList([
            {
              payeeAcount: accountList?.[0]?.value,
              payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan?.toString(),
              key: _.uniqueId('cashList'),
            },
          ]);
        } else {
          payKindParams.payDetailList = orderDetail.orderPayDetailList?.map((item) => ({
            payeeAcount: item.payeeAccount,
            payAmount: item.payAmountYuan,
            payChannel: item.payKind,
          }));
          if (onChange) {
            onChange(payKindParams);
          }
        }
    }
  }, [payKind, orderDetail]);

  /**
   * 更新结算方式
   */
  useEffect(() => {
    if (!orderDetail) {
      return;
    }
    if (!needUpdate.current) {
      return;
    }
    if (payKind === PayKind.Credit) {
      return;
    }
    let payKindParams = {
      orderId: orderDetail?.orderId,
      payKind: payKind,
      payDetailList: [],
    } as ConfirmPayRequest;

    cashList?.forEach((item: any) => {
      payKindParams.payDetailList?.push({
        ...item,
        payChannel: PayChannel.CASH,
      });
    });
    console.log('payKindParams', payKindParams);

    if (onChange) {
      onChange(payKindParams);
    } else {
      const oldPayDetailList = orderDetail?.orderPayDetailList?.map((item) => ({
        payChannel: item.payKind === PayKind.Cash ? PayChannel.CASH : PayChannel.PAYMENT_DAYS,
        payeeAcount: item.payeeAccount,
        payAmount: item.payAmountYuan?.toString(),
      }));
      const newPayDetailList = payKindParams.payDetailList?.map((item) => ({
        payChannel: item.payChannel,
        payeeAcount: item.payeeAcount,
        payAmount: item.payAmount,
      }));
      console.log(
        'compare',
        newPayDetailList,
        oldPayDetailList,
        _.isEqual(newPayDetailList, oldPayDetailList),
      );
      if (!_.isEqual(newPayDetailList, oldPayDetailList)) {
        let hasEmpty = false;
        payKindParams?.payDetailList?.forEach((item: any) => {
          if (!item?.payAmount || !item?.payeeAcount) {
            hasEmpty = true;
          }
        });

        if (payKindParams?.payDetailList?.length === 1 && hasEmpty) {
          payKindParams = {
            ...payKindParams,
            payDetailList: [
              {
                payChannel: PayChannel.CASH,
                payeeAcount: accountList?.[0]?.value,
                payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
              },
            ],
          };
          hasEmpty = false;
        }

        if (hasEmpty) {
          return;
        }

        updateOrderPayKind(payKindParams).then((result) => {
          if (result) {
            onRefresh?.();
          }
        });
      }
    }
  }, [cashList]);

  /**
   * 选择付款账户
   * @param selectedItems
   */
  const handelSelectAccount = (selectedItems: string[]) => {
    if (selectedItems.length === 0) {
      return;
    }
    if (cashList.length === 2 && cashList?.[0].payeeAcount === selectedItems[0]) {
      Taro.showToast({ title: '请选择不同的账户', icon: 'none' });
      return;
    }
    setCashList(
      cashList.map((item, index) =>
        index === accountPickerProps.index ? { ...item, payeeAcount: selectedItems[0] } : item,
      ),
    );
  };

  if (!orderDetail) {
    return null;
  }

  console.log('cashList', cashList);
  console.log('accountList', accountList);

  return (
    <Card title="结算方式" className={className}>
      <RadioButtonGroup
        value={payKind}
        onChange={(v) => setPayKind(v as PayKind)}
        options={payKindOptions(cstDetail?.settle?.credit === true)}
      />
      {payKind === PayKind.Credit && (
        <div className="text-secondary text-[28px] mt-[24px]">
          已用{cstDetail?.settle?.usedAmount ?? '-'}/可用
          {cstDetail?.settle?.availableAmount ?? '-'}
        </div>
      )}
      {payKind === PayKind.Cash && (
        <div className="text-[28px]">
          <div>
            {cashList?.map((item, index) => (
              <div className="flex items-center" key={item.key}>
                <div
                  className="flex self-center items-center"
                  onClick={() => setAccountPickerProps({ visible: true, index })}
                >
                  <div
                    className="overflow-ellipsis overflow-hidden whitespace-nowrap"
                    style={{ maxWidth: '300px' }}
                  >
                    {accountList?.find((m) => m.value === item?.payeeAcount)?.title ?? (
                      <div className="text-secondary">请选择</div>
                    )}
                  </div>
                  <ArrowDown size={20} className="ml-[10px]" color="#999999" />
                </div>
                <Input
                  className="flex-1 h-[108px] ml-[28px]"
                  placeholder="请输入金额"
                  value={item.payAmount}
                  onFocus={() => {
                    needUpdate.current = false;
                  }}
                  onChange={(v) => {
                    setCashList(
                      cashList.map((item, n) => (n === index ? { ...item, payAmount: v } : item)),
                    );
                  }}
                  onBlur={(v) => {
                    needUpdate.current = true;
                    if (!v) {
                      return;
                    }
                    const value = convertStringToNumber({
                      value: v,
                      min: 0,
                      max: 999999999.99,
                      decimal: 2,
                    });
                    const items = cashList.map((item, n) =>
                      n === index ? { ...item, payAmount: value.toString() } : item,
                    );
                    setCashList(items);
                  }}
                />
                {cashList?.length === 2 && (
                  <Trash
                    size={20}
                    color="#777777"
                    onClick={() => {
                      const newList = _.cloneDeep(cashList);
                      newList.splice(index, 1);
                      setCashList(newList);
                    }}
                  />
                )}
              </div>
            ))}
          </div>
          {cashList?.length === 1 && (
            <>
              <Divider />
              <div className="mt-[24px]">
                <div
                  className="flex text-[#F83431] justify-center items-center"
                  onClick={() => {
                    const item = [
                      ...cashList,
                      { payeeAcount: '', payAmount: undefined, key: _.uniqueId('addAccount') },
                    ];
                    setCashList(item);
                  }}
                >
                  <IconFont name={addIcon} style={{ width: '30px', marginRight: '10px' }} />
                  新增结算账户
                </div>
              </div>
            </>
          )}
        </div>
      )}
      <CustomPicker
        items={accountList}
        title="选择账户"
        visible={accountPickerProps.visible}
        selected={[cashList?.[accountPickerProps?.index!]?.payeeAcount]}
        onClose={() => setAccountPickerProps({ visible: false, index: undefined })}
        multiple={false}
        onConfirm={handelSelectAccount}
      />
    </Card>
  );
};

export default Settle;
