import Card from '@/components/Card';
import { CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import RadioButtonGroup from '@/components/RadioButtonGroup';
import {
  updateExtendExpense,
  updateOrderAddress,
  updateOrderDeliveryInfo,
} from '@/packages/sales/order/edit/services';
import {
  DeliveryMethod,
  deliveryMethodOptions,
} from '@/packages/sales/order/edit/types/DeliveryMethod';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { convertStringToNumber } from '@/utils/convertStringToNumber';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Cell, DatePicker, Divider, Form, Input, Picker } from '@nutui/nutui-react-taro';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';

export interface DeliveryProps {
  orderDetail?: OrderListItemEntity;
  cstDetail?: CustomerEntity;
  onRefresh?: () => void;
}

const Delivery = (props: DeliveryProps) => {
  const [form] = Form.useForm();
  const { orderDetail, cstDetail, onRefresh } = props;
  const [estimatedDeliveryTime, setEstimatedDeliveryTime] = useState<string>();
  const [pickerVisible, setPickerVisible] = useState(false);

  // 获取配送地址列表
  const addressList = useMemo(() => {
    const options: any = [];
    if (cstDetail?.addresses?.length) {
      cstDetail?.addresses.forEach((item) => {
        options.push({
          text: `${item.provinceName}${item.cityName}${item.prefectureName}${item.address}`,
          value: item.id,
        });
      });
    }
    return options;
  }, [cstDetail]);

  const distributionMode = orderDetail?.orderFixedDistributionList?.[0]?.distributionMode;

  useEffect(() => {
    if (orderDetail) {
      const formData = {
        deliveryMethod: distributionMode,
        addressId: [orderDetail?.orderFixedAddressList?.[0].addressId],
        deliveryAmount: orderDetail?.orderPrice?.deliveryAmountYuan?.toFixed(2),
      };
      form.setFieldsValue(formData);
      const _estimatedDeliveryTime =
        orderDetail?.orderFixedDistributionList?.[0].estimatedDeliveryTime;
      setEstimatedDeliveryTime(
        _estimatedDeliveryTime ? dayjs(_estimatedDeliveryTime).format('YYYY-MM-DD') : '',
      );
    }
  }, [orderDetail]);

  if (!orderDetail) {
    return;
  }

  return (
    <Card title="配送信息">
      <Form form={form}>
        <Form.Item name="deliveryMethod">
          <RadioButtonGroup
            defaultValue={DeliveryMethod.MERCHANT_DELIVERY}
            options={deliveryMethodOptions}
            onChange={(value) => {
              updateOrderDeliveryInfo({
                orderId: orderDetail?.orderId,
                deliveryMethod: value,
              }).then((result) => {
                if (result) {
                  onRefresh?.();
                }
              });
            }}
          />
        </Form.Item>
        {[DeliveryMethod.MERCHANT_DELIVERY, DeliveryMethod.EXPRESS_LOGISTICS].includes(
          distributionMode!,
        ) && (
          <Form.Item
            label="配送地址"
            name="addressId"
            trigger="onConfirm"
            getValueFromEvent={(...args) => args[1]}
            // @ts-ignore
            onClick={(event, ref: any) => {
              ref.open();
            }}
          >
            <Picker
              options={addressList}
              title="选择地址"
              onConfirm={(v) => {
                const address = cstDetail?.addresses?.find((item) => item.id === v?.[0].value);
                if (address) {
                  updateOrderAddress({ orderId: orderDetail?.orderId, ...address }).then(
                    (result) => {
                      if (result) {
                        onRefresh?.();
                      }
                    },
                  );
                }
              }}
            >
              {(value: any) => {
                return (
                  <Cell
                    title={
                      value.length ? (
                        addressList.filter((item) => item.value === value[0])[0]?.text
                      ) : (
                        <span className="text-thirdary">请选择</span>
                      )
                    }
                    extra={<ArrowRight />}
                  />
                );
              }}
            </Picker>
          </Form.Item>
        )}
        {distributionMode === DeliveryMethod.EXPRESS_LOGISTICS && (
          <>
            <Form.Item label="物流公司" name="logisticsCompanyName">
              <Input
                onBlur={(v) => {
                  updateOrderDeliveryInfo({
                    orderId: orderDetail?.orderId,
                    logisticsCompanyName: v,
                  }).then((result) => {
                    if (result) {
                      onRefresh?.();
                    }
                  });
                }}
              />
            </Form.Item>
            <Form.Item label="物流单号" name="logisticsNo">
              <Input
                onBlur={(v) => {
                  updateOrderDeliveryInfo({
                    orderId: orderDetail?.orderId,
                    logisticsNo: v,
                  }).then((result) => {
                    if (result) {
                      onRefresh?.();
                    }
                  });
                }}
              />
            </Form.Item>
          </>
        )}
        <Form.Item label="配送运费" name="deliveryAmount">
          <Input
            type="digit"
            clearable={false}
            onBlur={(v) => {
              if (!v) {
                return;
              }
              if (Number(v) === orderDetail?.orderPrice?.deliveryAmountYuan) {
                return;
              }
              const value = convertStringToNumber({
                value: v,
                min: 0,
                max: 999999999.99,
                decimal: 2,
              });
              if (value.toString() !== v) {
                form.setFieldsValue({ deliveryAmount: value });
              }
              updateExtendExpense({
                orderId: orderDetail?.orderId,
                deliveryAmount: value,
              }).then((result) => {
                if (result) {
                  onRefresh?.();
                }
              });
            }}
          />
        </Form.Item>
        <Divider />
        <Cell
          title="预计交货时间"
          extra={
            <span className="flex just-center">
              {estimatedDeliveryTime}
              <ArrowRight />
            </span>
          }
          style={{ marginTop: '20px' }}
          onClick={() => setPickerVisible(true)}
        />
      </Form>
      <DatePicker
        title="预计交货时间"
        startDate={dayjs().toDate()}
        defaultValue={dayjs().toDate()}
        visible={pickerVisible}
        onConfirm={(_options, values) => {
          const data = values.join('-');
          setEstimatedDeliveryTime(data);
          updateOrderDeliveryInfo({
            orderId: orderDetail?.orderId,
            estimatedDeliveryTime: data,
          }).then((result) => {
            if (result) {
              onRefresh?.();
            }
          });
        }}
        onClose={() => setPickerVisible(false)}
      />
    </Card>
  );
};

export default Delivery;
