import { queryMemberAccountPage } from '@/components/AccountSelectModal/services';
import { Address, CustomerEntity } from '@/components/ChooseCstModal/types/CustomerEntity';
import ChooseStoreAndWarehouse from '@/components/ChooseStoreAndWarehouse';
import CstCard from '@/components/CstCard';
import CustomNavBar from '@/components/CustomNavBar';
import Delivery from '@/packages/sales/order/edit/components/Delivery';
import Discount from '@/packages/sales/order/edit/components/Discount';
import FooterBar from '@/packages/sales/order/edit/components/FooterBar';
import Remark from '@/packages/sales/order/edit/components/Remark';
import SalesList from '@/packages/sales/order/edit/components/SalesList';
import ScanGood from '@/packages/sales/order/edit/components/ScanGood';
import Settle from '@/packages/sales/order/edit/components/Settle';
import { PayChannel } from '@/packages/sales/order/edit/components/Settle/types/PayChannel';
import { PayKind } from '@/packages/sales/order/edit/components/Settle/types/PayKind';
import {
  createDraftOrder,
  updateOrderItem,
  updateOrderMain,
} from '@/packages/sales/order/edit/services';
import { DeliveryMethod } from '@/packages/sales/order/edit/types/DeliveryMethod';
import { CreateDraftOrderRequest } from '@/packages/sales/order/edit/types/create.draft.order.request';
import { OprateType } from '@/packages/sales/order/edit/types/update.order.item.request';
import { convertStoreGoodsToSaleGoods } from '@/packages/sales/order/edit/utils/convertStoreGoodsToSaleGoods';
import { getOrderByOrderNoForDbReturnSelected } from '@/packages/sales/order/list/services';
import { OrderListItemEntity } from '@/packages/sales/order/list/types/order.list.item.entity';
import { scanCode } from '@/utils/scanCode';
import { IconFont } from '@nutui/icons-react-taro';
import { Dialog, Divider, SafeArea } from '@nutui/nutui-react-taro';
import { PickerOption } from '@nutui/nutui-react-taro/dist/types/packages/picker/types';
import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { ChooseItem } from '../chooseGoodsPage';
import scanIcon from './imgs/scan.svg';
import searchIcon from './imgs/search.svg';
import useOrderSaleStore from './store';

export default function Index() {
  const [storeAndWarehouseOptions, setStoreAndWarehouseOptions] = useState<PickerOption[]>([]);
  const [cstDetail, setCstDetail] = useState<CustomerEntity>();
  // 订单详情
  const [orderDetail, setOrderDetail] = useState<OrderListItemEntity>();
  const [accountList, setAccountList] = useState<any[]>([]);
  const [scanGoodProps, setScanGoodProps] = useState<{ visible: boolean; itemId?: string }>({
    visible: false,
  });

  const orderSaleStore = useOrderSaleStore();

  const router = useRouter();
  const { orderNo } = router.params;

  console.log('storeAndWarehouseOptions', storeAndWarehouseOptions);

  /**
   * 查询订单详情
   */
  const queryDetail = () => {
    if (orderNo) {
      getOrderByOrderNoForDbReturnSelected(orderNo).then((result) => {
        if (result) {
          setOrderDetail(result);
          setStoreAndWarehouseOptions([
            { text: result.orders?.storeName!, value: result.orders?.storeId! },
            {
              text: result.orderFixedDistributionList?.[0]?.warehouseName!,
              value: result.orderFixedDistributionList?.[0]?.warehouseId!,
            },
          ]);
        }
      });
    }
  };

  useEffect(() => {
    queryDetail();
  }, [orderNo]);

  useDidShow(() => {
    orderSaleStore.setModelId(undefined);
    orderSaleStore.setBaseInfo({});
    queryDetail();
  });

  useEffect(() => {
    console.log('edit => useEffect', orderSaleStore.chooseItems);
    if (orderSaleStore.chooseItems?.length) {
      setTimeout(() => {
        handleAddItem(orderSaleStore.chooseItems!);
        orderSaleStore.setChooseItems([]);
      }, 500);
    }
  }, [orderSaleStore.chooseItems]);

  useEffect(() => {
    if (storeAndWarehouseOptions?.[0]?.value) {
      queryMemberAccountPage({
        belongToStore: [storeAndWarehouseOptions?.[0]?.value as string],
      }).then((result) => {
        if (result?.data?.length) {
          setAccountList(
            result.data.map((item) => ({
              title: item.memberAccountName,
              value: item.id,
            })),
          );
        }
      });
    }
  }, [storeAndWarehouseOptions?.[0]?.value]);

  /**
   * 添加商品事件
   * @param items
   */
  const handleAddItem = (items: ChooseItem[]) => {
    if (orderNo) {
      const item = items[0];
      if (item.id) {
        updateOrderItem({
          orderId: orderDetail?.orderId,
          oprateType: OprateType.Modify,
          orderItemList: [{ id: item.id, unitPrice: item.price, saleNum: item.number }],
        }).then((result) => {
          if (result) {
            queryDetail();
          }
        });
      } else {
        const goods = convertStoreGoodsToSaleGoods(items);
        updateOrderItem({
          orderId: orderDetail?.orderId,
          oprateType: OprateType.Add,
          orderItemList: goods,
        }).then((result) => {
          if (result) {
            queryDetail();
          }
        });
      }
    } else {
      handleAddItemFn(items);
    }
  };

  /**
   * 通过商品创建草稿单
   */
  const handleAddItemFn = async (items: ChooseItem[]) => {
    console.log('edit => handleAddItemFn', items);
    const goods = convertStoreGoodsToSaleGoods(items);
    // 创建订单
    // 配送地址
    const address = cstDetail?.addresses?.find((item) => item.isDefault) ?? ({} as Address);
    // 默认联系人
    const contact = cstDetail?.contacts?.find((item) => item.isDefault);
    // 默认结算方式
    const payKind = cstDetail?.settle?.credit ? PayKind.Credit : PayKind.Cash;
    // 拼接草稿单入参
    const params: CreateDraftOrderRequest = {
      draftOrderMain: {
        cstId: cstDetail?.base?.id,
        cstName: cstDetail?.base?.cstName,
        storeId: storeAndWarehouseOptions[0].value as string,
        cstPhone: contact?.phone,
        storeName: storeAndWarehouseOptions[0].text as string,
      },
      draftOrderAddress: address,
      draftOrderPay: {
        payKind,
        payChannel: payKind === PayKind.Cash ? PayChannel.CASH : PayChannel.PAYMENT_DAYS,
      },
      draftOrderDelivery: {
        deliveryMethod: DeliveryMethod.MERCHANT_DELIVERY,
        warehouseId: storeAndWarehouseOptions[1].value! as string,
        warehouseName: storeAndWarehouseOptions[1].text! as string,
      },
      draftOrderItemList: goods,
    };
    // 当现款时默认账户设置
    if (payKind === PayKind.Cash) {
      params.draftOrderPay!.payeeAcount = accountList?.[0]?.value;
    }
    console.log('handleAddItemFn => params', params);
    createDraftOrder(params).then((result) => {
      console.log('handleAddItemFn => then', result);
      if (result?.orderNo) {
        Taro.showToast({ title: '添加成功', icon: 'none' });
        Taro.redirectTo({ url: `/packages/sales/order/edit/index?orderNo=${result.orderNo}` });
      }
    });
  };

  /**
   * 搜索商品的方式
   */
  const actions = [
    {
      label: '搜商品',
      icon: searchIcon,
      onClick: () => {
        orderSaleStore.setBaseInfo({
          warehouseId: storeAndWarehouseOptions[1]?.value as string,
          storeId: storeAndWarehouseOptions[0]?.value as string,
          cstId: cstDetail?.base?.id,
          orderNo: orderDetail?.orders?.orderNo,
        });
        Taro.navigateTo({
          url: '/packages/sales/order/chooseGoodsPage/index',
        });
      },
    },
    {
      label: '扫商品码',
      icon: scanIcon,
      onClick: () => {
        scanCode().then((result) => {
          setScanGoodProps({
            visible: true,
            itemId: result,
          });
        });
      },
    },
  ];

  return (
    <div className="pb-[300px]">
      <CustomNavBar title="销售开单" />
      <div className="px-[28px] mt-[28px]">
        <ChooseStoreAndWarehouse
          value={storeAndWarehouseOptions.map((item) => item.value)}
          onConfirm={(options) => {
            setStoreAndWarehouseOptions(options);
            if (orderDetail?.orderId && options?.[1].value !== storeAndWarehouseOptions[1]?.value) {
              // 更新仓库
              updateOrderMain({
                orderId: orderDetail?.orderId,
                warehouseName: options[1].text?.toString(),
                warehouseId: options[1].value?.toString(),
              }).then((result) => {
                if (result) {
                  queryDetail();
                }
              });
            }
          }}
          disabledColumns={[Boolean(orderNo), false]}
        />
        <CstCard
          className="mt-[24px]"
          onConfirm={(data) => {
            setCstDetail(data);
          }}
          cstId={orderDetail?.orders?.cstId ?? cstDetail?.base?.id}
          disabled={Boolean(orderNo)}
        />
        <div className="flex bg-white rounded-[16px] my-[24px] py-[36px] items-center">
          {actions.map((item, index) => {
            return (
              <>
                {index !== 0 && <Divider direction="vertical" />}
                <span
                  className={'flex-1 flex justify-center items-center'}
                  onClick={() => {
                    if (!cstDetail) {
                      Taro.showToast({ title: '请先选择客户', icon: 'none' });
                      return;
                    }
                    item.onClick();
                  }}
                >
                  <IconFont name={item.icon} style={{ width: '16px', marginRight: '8px' }} />
                  {item.label}
                </span>
              </>
            );
          })}
        </div>
      </div>
      <SalesList orderDetail={orderDetail} />
      <Discount orderDetail={orderDetail} onRefresh={queryDetail} />
      <Settle orderDetail={orderDetail} cstDetail={cstDetail} onRefresh={queryDetail} />
      <Delivery orderDetail={orderDetail} onRefresh={queryDetail} cstDetail={cstDetail} />
      <Remark orderDetail={orderDetail} onRefresh={queryDetail} />
      <FooterBar orderDetail={orderDetail} />
      <ScanGood
        {...scanGoodProps}
        storeId={storeAndWarehouseOptions[0]?.value as string}
        cstId={cstDetail?.base?.id}
        warehouseId={storeAndWarehouseOptions[1]?.value as string}
        onClose={() => setScanGoodProps({ visible: false, itemId: undefined })}
        onConfirm={(item) => handleAddItem([item])}
        orderDetail={orderDetail}
      />
      <SafeArea position="bottom" />
      <Dialog id="dialog" lockScroll />
    </div>
  );
}
