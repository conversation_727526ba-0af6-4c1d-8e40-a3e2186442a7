import CustomMultipleChoose from "@/components/CustomMultipleChoose";
import CustomNavBar from "@/components/CustomNavBar";
import FilterAccountPicker from "@/components/FilterAccountPicker";
import MenuWrap from "@/components/MenuWrap";
import FunPagination from "@/components/Pagination/FunPagination";
import { showToast } from "@/utils/financeUtils";
import { Add } from '@nutui/icons-react-taro';
import { Dialog, Menu, SafeArea, SearchBar } from "@nutui/nutui-react-taro";
import { useSetState } from "ahooks";
import { useRef, useState } from "react";
import CompleteTask from "../components/CompleteTask";
import EditTask from "../components/EditTask";
import TaskItem from "../components/TaskItem";
import { cancelTodo, completeTodo, createTodo, queryTodoList, updateTodo } from "../services";
import { CompleteTodoParams, CreateTodoParams, TodoEntity, UpdateTodoParams } from "../types";
import { StatusEnum, statusEnumMap } from "../types/todo.enum";

const statusPanelItems = [
  {
    keyStr: 'statuses',
    multiple: true,
    label: '',
    item: [
      {
        text: statusEnumMap[StatusEnum.NotCompleted].text,
        value: StatusEnum.NotCompleted,
      },
      {
        text: statusEnumMap[StatusEnum.Completed].text,
        value: StatusEnum.Completed,
      },
      {
        text: statusEnumMap[StatusEnum.Cancelled].text,
        value: StatusEnum.Cancelled,
      },
    ],
  },
];

export default function TaskList() {
  const statusRef = useRef<any>();
  const creatorRef = useRef<any>();
  const todoPersonRef = useRef<any>();
  const [params, setParams] = useState<Pick<Partial<TodoEntity>, 'taskDesc' | 'todoPerson' | 'createPerson'> & { statuses?: number[] }>({});


  const [state, setState] = useSetState({
    editVisible: false,
    completeVisible: false,
    currentTask: undefined as TodoEntity | undefined,
  });


  const setInputValue = (param) => {
    setParams((prevData) => ({ ...prevData, ...param }));
  };

  const fetchData = (query) => {
    return queryTodoList({ ...query, pageSize: 10 }).then(
      (result) => result?.data ?? [],
    );
  };

  const handleEdit = (record: TodoEntity) => {
    setState({ currentTask: record, editVisible: true });
  };


  const handleEditConfirm = async (data: CreateTodoParams | UpdateTodoParams) => {
    let result;
    if (state.currentTask) {
      result = await updateTodo(data as UpdateTodoParams);
    } else {
      result = await createTodo(data as CreateTodoParams);
    }
    if (result) {
      setState({ editVisible: false });
      showToast('提交成功');
      setParams({ ...params, timestamp: Date.now() });
    }
  };

  const handleComplete = (record: TodoEntity) => {
    setState({ currentTask: record, completeVisible: true });
  };

  const handleCompleteConfirm = async (params: CompleteTodoParams) => {
    console.log(params)
    const result = await completeTodo(params);
    if (result) {
      showToast('任务已完成');
      setState({ completeVisible: false });
      setParams({ ...params, timestamp: Date.now() });
    }
  };

  const handleCancel = (record: TodoEntity) => {
    Dialog.open('test', {
      title: '确认',
      content: '确定取消吗？',
      onConfirm: () => {
        Dialog.close('test')
        cancelTodo({ id: record.id }).then(() => {
          showToast('取消成功');
          setState({ currentTask: undefined, editVisible: true });
        })
      },
      onCancel: () => {
        Dialog.close('test')
      },
    })
  };

  return <div className="flex flex-col h-screen">
    <CustomNavBar showBack={true} title="待办任务" />

    <div className="pt-[16px] mb-[24px]">
      <SearchBar
        value={params.taskDesc}
        onChange={(val: string) => {
          setInputValue({ taskDesc: val });
        }}
        onClear={() => {
          setInputValue({ taskDesc: '' });
        }}
        placeholder={'任务描述'}
        right={<Add onClick={() => {
          setState({ currentTask: undefined, editVisible: true });
        }} />}
      />
    </div>
    <MenuWrap
      menu={
        <Menu>
          <Menu.Item title="创建人" ref={creatorRef}>
            <FilterAccountPicker
              accountId={params.createPerson}
              onChange={(accountId) => {
                setParams({ ...params, createPerson: accountId });
                creatorRef.current?.toggle(false);
              }}
              label="创建人"
            />
          </Menu.Item>
          <Menu.Item title="待办人" ref={todoPersonRef}>
            <FilterAccountPicker
              accountId={params.todoPerson}
              onChange={(accountId) => {
                setParams({ ...params, todoPerson: accountId });
                todoPersonRef.current?.toggle(false);
              }}
              label="待办人"
            />
          </Menu.Item>
          <Menu.Item title="完成状态" ref={statusRef}>
            <CustomMultipleChoose
              key={'statuses'}
              layout="vertical"
              onClose={() => {
                statusRef.current?.toggle(false);
              }}
              onConfirm={(e) => {
                setParams({ ...params, statuses: e.statuses });
              }}
              items={statusPanelItems}
            />
          </Menu.Item>
        </Menu>
      }
    />
    <div className="flex-1 min-h-0 overflow-scroll mt-[20px]">
      <FunPagination
        fetchData={fetchData}
        params={params}
        renderItem={(record, index) => <TaskItem
          record={record}
          onEdit={handleEdit}
          onComplete={handleComplete}
          onCancel={handleCancel}
        />}
      />
    </div>


    <EditTask
      visible={state.editVisible}
      onClose={() => setState({ editVisible: false })}
      onConfirm={handleEditConfirm}
      editData={state.currentTask}
      title={state.currentTask ? '编辑任务' : '新建任务'}
    />

    <CompleteTask
      visible={state.completeVisible}
      onClose={() => setState({ completeVisible: false })}
      onConfirm={handleCompleteConfirm}
      taskData={state.currentTask}
    />
    <Dialog id="comfirm" />
    <SafeArea position={'bottom'} />
  </div>;
}
