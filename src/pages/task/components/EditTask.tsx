import { accountListQuerySimple } from '@/services/common';
import { ArrowRight } from '@nutui/icons-react-taro';
import { Button, Cell, Divider, Form, Picker, Popup, SafeArea, TextArea } from '@nutui/nutui-react-taro';
import Taro from '@tarojs/taro';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { CreateTodoParams, TodoEntity, UpdateTodoParams } from '../types';

export interface EditTaskProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (params: CreateTodoParams | UpdateTodoParams) => void;
  editData?: TodoEntity; // 编辑时传入的数据
  title?: string;
}

const EditTask = (props: EditTaskProps) => {
  const { visible, onClose, onConfirm, editData, title } = props;
  const intl = useIntl();
  const defaultTitle = intl.formatMessage({ id: 'task.edit.title.create' });

  const [form] = Form.useForm()
  const taskDesc = Form.useWatch('taskDesc', form)

  const [persons, setPersons] = useState<{ text: string, value: string }[]>([])

  useEffect(() => {
    accountListQuerySimple({}).then(res => {
      setPersons(res.map(item => ({ text: item.name, value: item.id })))
    })
  }, [])

  // 编辑模式下初始化数据
  useEffect(() => {
    console.log(editData)
    if (editData) {
      form.setFieldsValue({
        taskDesc: editData.taskDesc,
        todoPerson: [editData.todoPerson],
      })
    } else {
      form.resetFields()
    }
  }, [editData, visible]);

  // 处理确认
  const handleConfirm = (values) => {
    const todoPerson = values.todoPerson[0];

    onConfirm({
      ...values,
      id: editData?.id,
      todoPerson,
      todoPersonName: persons.find((item) => item.value === todoPerson)?.text,
    });
  };

  // 处理取消
  const handleCancel = () => {
    onClose();
  };

  const submitFailed = (error: any) => {
    Taro.showToast({ title: JSON.stringify(error), icon: 'error' })
  }

  return (

    <Popup
      visible={visible}
      position="bottom"
      onClose={handleCancel}
      closeable
      closeIcon="close"
      title={title || defaultTitle}
    >
      <div className='px-[28px] py-[24px]'>

        <Form
          divider={false}
          form={form}
          footer={
            <div className="flex space-x-[24px] w-full">
              <Button
                block
                fill="outline"
                size="large"
                onClick={handleCancel}
                className="flex-1"
              >
                {intl.formatMessage({ id: 'task.button.cancel' })}
              </Button>
              <Button
                block
                type="primary"
                size="large"
                className="flex-1"
                nativeType="submit"
              >
                {intl.formatMessage({ id: 'task.button.confirm' })}
              </Button>
            </div>
          }
          onFinish={(values) => handleConfirm(values)}
          onFinishFailed={(values, errors) => submitFailed(errors)}
        >
          {/* 任务描述 */}
          <div className="mb-[32px]">
            <div className="flex justify-between items-center mb-[16px] text-[28px]">
              <span>
                <span className="text-primary">{intl.formatMessage({ id: 'task.edit.taskDesc.required' })}</span>
                {intl.formatMessage({ id: 'task.edit.taskDesc.label' })}
              </span>
              <span className="text-[24px] text-[#0000004D]">
                {intl.formatMessage({ id: 'task.edit.taskDesc.counter' }, { current: taskDesc?.length ?? 0 })}
              </span>
            </div>
          </div>
          <Form.Item required
            name="taskDesc"
            rules={[{ max: 100, min: 0, required: true }]}
          >
            <TextArea
              placeholder={intl.formatMessage({ id: 'task.edit.taskDesc.placeholder' })}
              maxLength={100}
              autoSize
              rows={6}
            />
          </Form.Item>
          <Divider />
          <Form.Item
            label={intl.formatMessage({ id: 'task.edit.todoPerson.label' })}
            name="todoPerson"
            trigger="onConfirm"
            getValueFromEvent={(...args) => args[1]}
            onClick={(event, ref: any) => {
              ref.open()
            }}
            required
            rules={[{ required: true }]}
          >
            <Picker options={[persons]}>
              {(value: any) => {
                return (
                  <Cell
                    className="nutui-cell--clickable"
                    title={
                      value.length
                        ? persons.filter((po) => po.value === value[0])[0]
                          ?.text
                        : <span className='text-[#757575]'>{intl.formatMessage({ id: 'task.edit.todoPerson.placeholder' })}</span>
                    }
                    extra={<ArrowRight />}
                    align="center"
                  />
                )
              }}
            </Picker>
          </Form.Item>
        </Form>
      </div>
      <SafeArea position="bottom" />
    </Popup >


  );
};

export default EditTask;