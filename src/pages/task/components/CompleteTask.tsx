import { <PERSON><PERSON>, <PERSON>up, SafeArea, TextArea } from '@nutui/nutui-react-taro';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';
import { CompleteTodoParams, TodoEntity } from '../types';
import { StatusEnum } from '../types/todo.enum';

export interface CompleteTaskProps {
  visible: boolean;
  onClose: () => void;
  onConfirm?: (params: CompleteTodoParams) => void;
  taskData?: TodoEntity | null;
}

const CompleteTask = (props: CompleteTaskProps) => {
  const { visible, onClose, onConfirm, taskData } = props;
  const intl = useIntl();

  const readonly = taskData?.status === StatusEnum.Completed;

  const [completionDesc, setCompletionDesc] = useState('');

  // 初始化数据
  useEffect(() => {
    if (taskData) {
      setCompletionDesc(taskData.completionDesc || '');
    } else {
      setCompletionDesc('');
    }
  }, [taskData, visible]);

  // 处理确认完成
  const handleConfirm = () => {
    if (!taskData) return;

    const params: CompleteTodoParams = {
      id: taskData.id,
      completionDesc: completionDesc.trim(),
    };

    onConfirm?.(params);
  };

  // 处理取消
  const handleCancel = () => {
    setCompletionDesc('');
    onClose();
  };

  if (!taskData) return null;

  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={handleCancel}
      closeable
      closeIcon="close"
      title={readonly ? intl.formatMessage({ id: 'task.complete.title.view' }) : intl.formatMessage({ id: 'task.complete.title.complete' })}
    >
      <div className="px-[28px] py-[24px]">
        {/* 完成说明 */}
        <div className="mb-[48px]">
          {!readonly &&
            <div className="flex justify-between items-center mb-[16px] mx-3">
              <span className="text-[32px] text-[#111111]">
                {intl.formatMessage({ id: 'task.complete.desc.label' })}
              </span>
              <span className="text-[24px] text-[#999999]">
                {intl.formatMessage({ id: 'task.complete.desc.counter' }, { current: completionDesc.length })}
              </span>
            </div>}
          {readonly ? (
            <div className="text-[28px] text-[#666666] leading-[40px] p-[24px] min-h-[120px]">
              {taskData.completionDesc || intl.formatMessage({ id: 'task.complete.desc.empty' })}
            </div>
          ) : (
            <TextArea
              placeholder={intl.formatMessage({ id: 'task.complete.desc.placeholder' })}
              value={completionDesc}
              maxLength={100}
              autoSize
              rows={3}
              onChange={setCompletionDesc}
            />
          )}
        </div>

        {/* 操作按钮 */}
        {!readonly && (
          <div className="flex space-x-[24px]">
            <Button
              block
              fill="outline"
              size="large"
              onClick={handleCancel}
              className="flex-1"
            >
              {intl.formatMessage({ id: 'task.button.cancel' })}
            </Button>
            <Button
              block
              type="primary"
              size="large"
              onClick={handleConfirm}
              className="flex-1"
            >
              {intl.formatMessage({ id: 'task.complete.action.confirm' })}
            </Button>
          </div>
        )}
      </div>
      <SafeArea position="bottom" />
    </Popup>
  );
};

export default CompleteTask;