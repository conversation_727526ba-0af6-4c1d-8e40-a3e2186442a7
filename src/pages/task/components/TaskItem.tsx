import { TimeFormat } from '@/components/TimeFormat';
import useUserStore from '@/stores/user';
import { Notepad } from '@nutui/icons-react-taro';
import { Tag } from '@nutui/nutui-react-taro';
import { TodoEntity } from '../types';
import { StatusEnum, statusEnumMap } from '../types/todo.enum';

export interface TaskItemProps {
  record: TodoEntity;
  onEdit?: (record: TodoEntity) => void;
  onComplete?: (record: TodoEntity) => void;
  onCancel?: (record: TodoEntity) => void;
}

const TaskItem = (props: TaskItemProps) => {
  const { record, onEdit, onComplete, onCancel } = props;
  const userStore = useUserStore();
  const currentUser = userStore.userInfo;
  console.log(currentUser)

  return (
    <div className="bg-white rounded-[16px] mx-[28px] mb-[24px] px-[28px] py-[30px]">
      {/* 头部：时间和状态 */}
      <div className="flex items-center mb-[16px]">
        <span className="text-[32px]">
          <TimeFormat showTime time={record.createTime} />
        </span>
        <Tag
          color={statusEnumMap[record.status].color}
          type={statusEnumMap[record.status].type}
          className='ml-2'
        >
          {statusEnumMap[record.status].text}
        </Tag>
        {
          record.completionDesc && <Notepad className='ml-1' color='#000000CC' onClick={(e) => {
            onComplete?.(record);
          }} />
        }
      </div>

      {/* 创建人和待办人信息 */}
      <div className="text-[24px] mt-[12px] text-[#999]">
        创建人：{record.createPerson} | 待办人：{record.todoPersonName}
      </div>

      {/* 任务描述 */}
      <div className="text-[28px] text-[#666] mt-[16px] leading-[44px]">
        {record.taskDesc}
      </div>

      {/* 时间信息 */}
      {record.completionTime && <div className="text-[24px] text-[#999999] mt-[16px]">
        完成时间： <TimeFormat showTime time={record.completionTime} />
      </div>}

      {/* 操作按钮 */}
      <div className="flex space-x-[16px] text-[28px] mt-[16px]">
        {record.status == StatusEnum.NotCompleted && currentUser?.id === record.createPerson && <a
          onClick={(e) => {
            e.stopPropagation();
            onEdit?.(record);
          }}
        >
          编辑
        </a>}
        {record.status == StatusEnum.NotCompleted && currentUser?.id === record.todoPerson && <a
          onClick={(e) => {
            e.stopPropagation();
            onComplete?.(record);
          }}
        >
          完成
        </a>}
        {record.status == StatusEnum.NotCompleted && currentUser?.id === record.createPerson && (
          <a
            onClick={(e) => {
              e.stopPropagation();
              onCancel?.(record);
            }}
          >
            取消
          </a>
        )}
      </div>
    </div>
  );
};

export default TaskItem;